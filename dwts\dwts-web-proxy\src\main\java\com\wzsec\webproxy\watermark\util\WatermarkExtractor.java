package com.wzsec.webproxy.watermark.util;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 水印提取和溯源工具类
 * 用于从带有暗水印的内容中提取水印信息，支持溯源追踪
 *
 * <AUTHOR>
 * @date 2025/08/05
 */
@Slf4j
@Component
public class WatermarkExtractor {

    private final ObjectMapper objectMapper = new ObjectMapper();
    
    // 零宽字符定义（与InvisibleWatermarkProcessor保持一致）
    private static final char ZERO_WIDTH_SPACE = '\u200B';
    private static final char ZERO_WIDTH_NON_JOINER = '\u200C';
    private static final char ZERO_WIDTH_JOINER = '\u200D';
    private static final char WORD_JOINER = '\u2060';
    private static final char INVISIBLE_SEPARATOR = '\u2062';
    
    // 解码映射表
    private static final Map<Character, String> DECODING_MAP = new HashMap<>();
    static {
        DECODING_MAP.put(ZERO_WIDTH_SPACE, "00");
        DECODING_MAP.put(ZERO_WIDTH_NON_JOINER, "01");
        DECODING_MAP.put(ZERO_WIDTH_JOINER, "10");
        DECODING_MAP.put(WORD_JOINER, "11");
    }

    /**
     * 从内容中提取水印信息
     *
     * @param content 包含水印的内容
     * @param contentType 内容类型
     * @return 提取的水印信息列表
     */
    public List<ExtractedWatermark> extractWatermarks(String content, String contentType) {
        List<ExtractedWatermark> watermarks = new ArrayList<>();
        
        try {
            if (isJsonContent(contentType)) {
                watermarks.addAll(extractFromJson(content));
            } else if (isXmlContent(contentType)) {
                watermarks.addAll(extractFromXml(content));
            } else {
                watermarks.addAll(extractFromText(content));
            }
            
            log.info("从内容中提取到 {} 个水印", watermarks.size());
            
        } catch (Exception e) {
            log.error("水印提取失败", e);
        }
        
        return watermarks;
    }

    /**
     * 从JSON内容中提取水印
     */
    private List<ExtractedWatermark> extractFromJson(String jsonContent) throws Exception {
        List<ExtractedWatermark> watermarks = new ArrayList<>();
        JsonNode rootNode = objectMapper.readTree(jsonContent);
        
        extractFromJsonNode(rootNode, "", watermarks);
        
        return watermarks;
    }

    /**
     * 递归从JSON节点中提取水印
     */
    private void extractFromJsonNode(JsonNode node, String path, List<ExtractedWatermark> watermarks) {
        if (node.isObject()) {
            node.fields().forEachRemaining(entry -> {
                String fieldPath = path.isEmpty() ? entry.getKey() : path + "." + entry.getKey();
                if (entry.getValue().isTextual()) {
                    List<WatermarkInfo> infos = extractInvisibleWatermarks(entry.getValue().asText());
                    for (WatermarkInfo info : infos) {
                        watermarks.add(new ExtractedWatermark(fieldPath, info));
                    }
                } else {
                    extractFromJsonNode(entry.getValue(), fieldPath, watermarks);
                }
            });
        } else if (node.isArray()) {
            for (int i = 0; i < node.size(); i++) {
                String arrayPath = path + "[" + i + "]";
                JsonNode element = node.get(i);
                if (element.isTextual()) {
                    List<WatermarkInfo> infos = extractInvisibleWatermarks(element.asText());
                    for (WatermarkInfo info : infos) {
                        watermarks.add(new ExtractedWatermark(arrayPath, info));
                    }
                } else {
                    extractFromJsonNode(element, arrayPath, watermarks);
                }
            }
        }
    }

    /**
     * 从XML内容中提取水印
     */
    private List<ExtractedWatermark> extractFromXml(String xmlContent) {
        List<ExtractedWatermark> watermarks = new ArrayList<>();
        
        // 使用正则表达式提取XML文本节点
        Pattern textNodePattern = Pattern.compile(">([^<]+)<");
        Matcher matcher = textNodePattern.matcher(xmlContent);
        
        int nodeIndex = 0;
        while (matcher.find()) {
            String textContent = matcher.group(1);
            List<WatermarkInfo> infos = extractInvisibleWatermarks(textContent);
            for (WatermarkInfo info : infos) {
                watermarks.add(new ExtractedWatermark("xml.textNode[" + nodeIndex + "]", info));
            }
            nodeIndex++;
        }
        
        return watermarks;
    }

    /**
     * 从纯文本中提取水印
     */
    private List<ExtractedWatermark> extractFromText(String textContent) {
        List<ExtractedWatermark> watermarks = new ArrayList<>();
        List<WatermarkInfo> infos = extractInvisibleWatermarks(textContent);
        
        for (WatermarkInfo info : infos) {
            watermarks.add(new ExtractedWatermark("text", info));
        }
        
        return watermarks;
    }

    /**
     * 从文本中提取不可见水印
     */
    private List<WatermarkInfo> extractInvisibleWatermarks(String text) {
        List<WatermarkInfo> watermarks = new ArrayList<>();
        
        if (text == null || text.isEmpty()) {
            return watermarks;
        }
        
        try {
            // 查找零宽字符序列
            List<String> zeroWidthSequences = findZeroWidthSequences(text);
            
            for (String sequence : zeroWidthSequences) {
                try {
                    String decodedData = decodeFromZeroWidthChars(sequence);
                    WatermarkInfo info = parseWatermarkData(decodedData);
                    if (info != null) {
                        watermarks.add(info);
                    }
                } catch (Exception e) {
                    log.debug("解码零宽字符序列失败: {}", e.getMessage());
                }
            }
            
        } catch (Exception e) {
            log.warn("提取不可见水印失败: {}", e.getMessage());
        }
        
        return watermarks;
    }

    /**
     * 查找零宽字符序列（改进版本）
     */
    private List<String> findZeroWidthSequences(String text) {
        List<String> sequences = new ArrayList<>();
        StringBuilder currentSequence = new StringBuilder();
        boolean inSequence = false;

        log.debug("开始查找零宽字符序列，文本长度: {}", text.length());

        for (int i = 0; i < text.length(); i++) {
            char c = text.charAt(i);

            if (c == INVISIBLE_SEPARATOR) {
                if (inSequence) {
                    // 结束当前序列
                    if (currentSequence.length() > 0) {
                        String sequence = currentSequence.toString();
                        sequences.add(sequence);
                        log.debug("发现完整序列，长度: {} 字符", sequence.length());
                        currentSequence.setLength(0);
                    }
                    inSequence = false;
                } else {
                    // 开始新序列
                    inSequence = true;
                    log.debug("开始新的零宽字符序列，位置: {}", i);
                }
            } else if (DECODING_MAP.containsKey(c)) {
                if (inSequence) {
                    currentSequence.append(c);
                } else {
                    // 发现零宽字符但不在序列中，可能是没有分隔符的序列
                    // 开始收集连续的零宽字符
                    currentSequence.append(c);
                    inSequence = true;
                    log.debug("发现零宽字符序列（无起始分隔符），位置: {}", i);
                }
            } else if (inSequence) {
                // 遇到非零宽字符，结束序列
                if (currentSequence.length() > 0) {
                    String sequence = currentSequence.toString();
                    sequences.add(sequence);
                    log.debug("序列被非零宽字符终止，长度: {} 字符", sequence.length());
                    currentSequence.setLength(0);
                }
                inSequence = false;
            }
        }

        // 处理未结束的序列
        if (currentSequence.length() > 0) {
            String sequence = currentSequence.toString();
            sequences.add(sequence);
            log.debug("发现末尾序列，长度: {} 字符", sequence.length());
        }

        log.info("总共发现 {} 个零宽字符序列", sequences.size());
        return sequences;
    }

    /**
     * 从零宽字符解码为字符串（改进版本）
     */
    private String decodeFromZeroWidthChars(String zeroWidthSequence) {
        StringBuilder binaryBuilder = new StringBuilder();

        log.debug("开始解码零宽字符序列，长度: {} 字符", zeroWidthSequence.length());

        // 将零宽字符转换为二进制
        for (int i = 0; i < zeroWidthSequence.length(); i++) {
            char c = zeroWidthSequence.charAt(i);

            if (c == INVISIBLE_SEPARATOR) {
                log.debug("跳过分隔符，位置: {}", i);
                continue;
            }

            String bits = DECODING_MAP.get(c);
            if (bits != null) {
                binaryBuilder.append(bits);
                log.debug("字符 \\u{} -> {}", String.format("%04X", (int) c), bits);
            } else {
                log.warn("未知的零宽字符: \\u{}", String.format("%04X", (int) c));
            }
        }

        String binaryString = binaryBuilder.toString();
        log.debug("二进制字符串: {} (长度: {})", binaryString, binaryString.length());

        if (binaryString.length() % 8 != 0) {
            log.warn("二进制长度 {} 不是8的倍数，可能解码不完整", binaryString.length());
        }

        StringBuilder result = new StringBuilder();

        // 每8位二进制转换为一个字符
        for (int i = 0; i < binaryString.length(); i += 8) {
            if (i + 8 <= binaryString.length()) {
                String byteString = binaryString.substring(i, i + 8);
                try {
                    int charCode = Integer.parseInt(byteString, 2);
                    char decodedChar = (char) charCode;
                    result.append(decodedChar);
                    log.debug("二进制 {} -> 字符码 {} -> 字符 '{}'", byteString, charCode,
                             isPrintable(decodedChar) ? decodedChar : "\\u" + String.format("%04X", (int) decodedChar));
                } catch (NumberFormatException e) {
                    log.warn("无法解析二进制字符串: {}", byteString);
                }
            }
        }

        String decodedText = result.toString();
        log.debug("解码结果: '{}'", decodedText);
        return decodedText;
    }

    /**
     * 判断字符是否可打印
     */
    private boolean isPrintable(char c) {
        return c >= 32 && c <= 126;
    }

    /**
     * 解析水印数据
     */
    private WatermarkInfo parseWatermarkData(String watermarkData) {
        try {
            String[] parts = watermarkData.split("\\|");
            if (parts.length < 5) {
                return null;
            }
            
            String userId = parts[0];
            String ipAddress = parts[1];
            long timestamp = Long.parseLong(parts[2]);
            String sessionId = parts[3];
            String checksum = parts[4];
            
            // 验证校验和
            String dataToVerify = String.join("|", Arrays.copyOf(parts, 4));
            if (!verifyChecksum(dataToVerify, checksum)) {
                log.warn("水印校验和验证失败");
                return null;
            }
            
            return WatermarkInfo.builder()
                .userId(userId)
                .ipAddress(ipAddress)
                .timestamp(timestamp)
                .sessionId(sessionId)
                .build();
                
        } catch (Exception e) {
            log.debug("解析水印数据失败: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 验证校验和
     */
    private boolean verifyChecksum(String data, String expectedChecksum) {
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] hash = md.digest(data.getBytes(StandardCharsets.UTF_8));
            String actualChecksum = Base64.getEncoder().encodeToString(hash).substring(0, 4);
            return actualChecksum.equals(expectedChecksum);
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 生成溯源报告
     */
    public TraceReport generateTraceReport(List<ExtractedWatermark> watermarks) {
        TraceReport report = new TraceReport();
        report.setExtractTime(System.currentTimeMillis());
        report.setWatermarkCount(watermarks.size());
        
        Map<String, Integer> userStats = new HashMap<>();
        Map<String, Integer> ipStats = new HashMap<>();
        Set<String> sessions = new HashSet<>();
        
        for (ExtractedWatermark watermark : watermarks) {
            WatermarkInfo info = watermark.getWatermarkInfo();
            
            // 统计用户
            userStats.merge(info.getUserId(), 1, Integer::sum);
            
            // 统计IP
            ipStats.merge(info.getIpAddress(), 1, Integer::sum);
            
            // 收集会话
            sessions.add(info.getSessionId());
        }
        
        report.setUserStatistics(userStats);
        report.setIpStatistics(ipStats);
        report.setUniqueSessionCount(sessions.size());
        report.setWatermarksDetails(watermarks);
        
        return report;
    }

    private boolean isJsonContent(String contentType) {
        return contentType != null && 
               (contentType.toLowerCase().contains("application/json") ||
                contentType.toLowerCase().contains("text/json"));
    }

    private boolean isXmlContent(String contentType) {
        return contentType != null && 
               (contentType.toLowerCase().contains("application/xml") ||
                contentType.toLowerCase().contains("text/xml"));
    }

    /**
     * 提取的水印信息
     */
    public static class ExtractedWatermark {
        private String location;
        private WatermarkInfo watermarkInfo;
        
        public ExtractedWatermark(String location, WatermarkInfo watermarkInfo) {
            this.location = location;
            this.watermarkInfo = watermarkInfo;
        }
        
        // Getters
        public String getLocation() { return location; }
        public WatermarkInfo getWatermarkInfo() { return watermarkInfo; }
    }

    /**
     * 水印信息
     */
    public static class WatermarkInfo {
        private String userId;
        private String ipAddress;
        private long timestamp;
        private String sessionId;

        public static WatermarkInfoBuilder builder() {
            return new WatermarkInfoBuilder();
        }

        // Getters
        public String getUserId() { return userId; }
        public String getIpAddress() { return ipAddress; }
        public long getTimestamp() { return timestamp; }
        public String getSessionId() { return sessionId; }

        public static class WatermarkInfoBuilder {
            private WatermarkInfo info = new WatermarkInfo();
            
            public WatermarkInfoBuilder userId(String userId) {
                info.userId = userId;
                return this;
            }
            
            public WatermarkInfoBuilder ipAddress(String ipAddress) {
                info.ipAddress = ipAddress;
                return this;
            }
            
            public WatermarkInfoBuilder timestamp(long timestamp) {
                info.timestamp = timestamp;
                return this;
            }
            
            public WatermarkInfoBuilder sessionId(String sessionId) {
                info.sessionId = sessionId;
                return this;
            }
            
            public WatermarkInfo build() {
                return info;
            }
        }
    }

    /**
     * 溯源报告
     */
    public static class TraceReport {
        private long extractTime;
        private int watermarkCount;
        private Map<String, Integer> userStatistics;
        private Map<String, Integer> ipStatistics;
        private int uniqueSessionCount;
        private List<ExtractedWatermark> watermarksDetails;

        // Getters and Setters
        public long getExtractTime() { return extractTime; }
        public void setExtractTime(long extractTime) { this.extractTime = extractTime; }
        
        public int getWatermarkCount() { return watermarkCount; }
        public void setWatermarkCount(int watermarkCount) { this.watermarkCount = watermarkCount; }
        
        public Map<String, Integer> getUserStatistics() { return userStatistics; }
        public void setUserStatistics(Map<String, Integer> userStatistics) { this.userStatistics = userStatistics; }
        
        public Map<String, Integer> getIpStatistics() { return ipStatistics; }
        public void setIpStatistics(Map<String, Integer> ipStatistics) { this.ipStatistics = ipStatistics; }
        
        public int getUniqueSessionCount() { return uniqueSessionCount; }
        public void setUniqueSessionCount(int uniqueSessionCount) { this.uniqueSessionCount = uniqueSessionCount; }
        
        public List<ExtractedWatermark> getWatermarksDetails() { return watermarksDetails; }
        public void setWatermarksDetails(List<ExtractedWatermark> watermarksDetails) { this.watermarksDetails = watermarksDetails; }
    }
}
