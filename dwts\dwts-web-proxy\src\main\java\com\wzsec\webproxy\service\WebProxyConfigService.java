package com.wzsec.webproxy.service;

import com.wzsec.webproxy.domain.WebProxyConfig;
import com.wzsec.webproxy.repository.WebProxyConfigRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Web代理配置服务
 *
 * <AUTHOR>
 * @date 2025/08/05
 */
@Slf4j
@Service
public class WebProxyConfigService {

    @Autowired
    private WebProxyConfigRepository configRepository;

    // 端口配置缓存
    private final Map<Integer, WebProxyConfig> portConfigCache = new ConcurrentHashMap<>();

    @PostConstruct
    public void initCache() {
        loadAllConfigs();
    }

    /**
     * 加载所有激活的配置到缓存
     */
    public void loadAllConfigs() {
        try {
            List<WebProxyConfig> configs = configRepository.findByStatusOrderByProxyPortAsc("ACTIVE");
            portConfigCache.clear();
            
            for (WebProxyConfig config : configs) {
                portConfigCache.put(config.getProxyPort(), config);
                log.info("加载代理配置: {} -> {}:{}", 
                        config.getProxyPort(), 
                        config.getTargetHost(), 
                        config.getTargetPort());
            }
            
            log.info("代理配置加载完成，共{}个配置", configs.size());
            
        } catch (Exception e) {
            log.error("加载代理配置失败", e);
        }
    }

    /**
     * 根据代理端口获取配置
     */
    @Cacheable(value = "proxyConfig", key = "#port")
    public WebProxyConfig getConfigByPort(Integer port) {
        // 先从缓存获取
        WebProxyConfig config = portConfigCache.get(port);
        if (config != null && config.isValid()) {
            return config;
        }
        
        // 从数据库查询
        Optional<WebProxyConfig> optional = configRepository.findByProxyPortAndStatus(port, "ACTIVE");
        if (optional.isPresent()) {
            config = optional.get();
            // 更新缓存
            portConfigCache.put(port, config);
            return config;
        }
        
        return null;
    }

    /**
     * 根据代理名称获取配置
     */
    @Cacheable(value = "proxyConfig", key = "'name:' + #proxyName")
    public WebProxyConfig getConfigByName(String proxyName) {
        Optional<WebProxyConfig> optional = configRepository.findByProxyNameAndStatus(proxyName, "ACTIVE");
        return optional.orElse(null);
    }

    /**
     * 获取所有激活的配置
     */
    public List<WebProxyConfig> getAllActiveConfigs() {
        return configRepository.findByStatusOrderByProxyPortAsc("ACTIVE");
    }

    /**
     * 保存配置
     */
    @Transactional
    @CacheEvict(value = "proxyConfig", allEntries = true)
    public WebProxyConfig saveConfig(WebProxyConfig config) {
        // 验证配置
        validateConfig(config);
        
        // 保存配置
        WebProxyConfig savedConfig = configRepository.save(config);
        
        // 更新缓存
        if ("ACTIVE".equals(savedConfig.getStatus())) {
            portConfigCache.put(savedConfig.getProxyPort(), savedConfig);
        } else {
            portConfigCache.remove(savedConfig.getProxyPort());
        }
        
        log.info("保存代理配置: {}", savedConfig.getProxyName());
        return savedConfig;
    }

    /**
     * 更新配置
     */
    @Transactional
    @CacheEvict(value = "proxyConfig", allEntries = true)
    public WebProxyConfig updateConfig(WebProxyConfig config) {
        if (config.getId() == null) {
            throw new IllegalArgumentException("配置ID不能为空");
        }
        
        // 验证配置
        validateConfig(config);
        
        // 更新配置
        WebProxyConfig updatedConfig = configRepository.save(config);
        
        // 更新缓存
        if ("ACTIVE".equals(updatedConfig.getStatus())) {
            portConfigCache.put(updatedConfig.getProxyPort(), updatedConfig);
        } else {
            portConfigCache.remove(updatedConfig.getProxyPort());
        }
        
        log.info("更新代理配置: {}", updatedConfig.getProxyName());
        return updatedConfig;
    }

    /**
     * 删除配置
     */
    @Transactional
    @CacheEvict(value = "proxyConfig", allEntries = true)
    public void deleteConfig(Long id) {
        Optional<WebProxyConfig> optional = configRepository.findById(id);
        if (optional.isPresent()) {
            WebProxyConfig config = optional.get();
            
            // 从缓存中移除
            portConfigCache.remove(config.getProxyPort());
            
            // 从数据库删除
            configRepository.deleteById(id);
            
            log.info("删除代理配置: {}", config.getProxyName());
        }
    }

    /**
     * 启用配置
     */
    @Transactional
    @CacheEvict(value = "proxyConfig", allEntries = true)
    public void enableConfig(Long id) {
        Optional<WebProxyConfig> optional = configRepository.findById(id);
        if (optional.isPresent()) {
            WebProxyConfig config = optional.get();
            config.setStatus("ACTIVE");
            
            WebProxyConfig savedConfig = configRepository.save(config);
            portConfigCache.put(savedConfig.getProxyPort(), savedConfig);
            
            log.info("启用代理配置: {}", config.getProxyName());
        }
    }

    /**
     * 禁用配置
     */
    @Transactional
    @CacheEvict(value = "proxyConfig", allEntries = true)
    public void disableConfig(Long id) {
        Optional<WebProxyConfig> optional = configRepository.findById(id);
        if (optional.isPresent()) {
            WebProxyConfig config = optional.get();
            config.setStatus("INACTIVE");
            
            configRepository.save(config);
            portConfigCache.remove(config.getProxyPort());
            
            log.info("禁用代理配置: {}", config.getProxyName());
        }
    }

    /**
     * 检查端口是否可用
     */
    public boolean isPortAvailable(Integer port, Long excludeId) {
        return !configRepository.existsByProxyPortAndStatusActive(port, excludeId);
    }

    /**
     * 获取配置统计信息
     */
    public Map<String, Object> getConfigStats() {
        Map<String, Object> stats = new ConcurrentHashMap<>();
        
        long totalCount = configRepository.count();
        long activeCount = configRepository.countByStatus("ACTIVE");
        long inactiveCount = configRepository.countByStatus("INACTIVE");
        
        stats.put("totalCount", totalCount);
        stats.put("activeCount", activeCount);
        stats.put("inactiveCount", inactiveCount);
        stats.put("cachedCount", portConfigCache.size());
        
        return stats;
    }

    /**
     * 刷新配置缓存
     */
    @CacheEvict(value = "proxyConfig", allEntries = true)
    public void refreshCache() {
        loadAllConfigs();
        log.info("代理配置缓存已刷新");
    }

    /**
     * 验证配置
     */
    private void validateConfig(WebProxyConfig config) {
        if (config.getProxyPort() == null || config.getProxyPort() <= 0 || config.getProxyPort() > 65535) {
            throw new IllegalArgumentException("代理端口必须在1-65535之间");
        }
        
        if (config.getTargetHost() == null || config.getTargetHost().trim().isEmpty()) {
            throw new IllegalArgumentException("目标主机地址不能为空");
        }
        
        if (config.getTargetPort() == null || config.getTargetPort() <= 0 || config.getTargetPort() > 65535) {
            throw new IllegalArgumentException("目标端口必须在1-65535之间");
        }
        
        // 检查端口是否已被使用
        if (!isPortAvailable(config.getProxyPort(), config.getId())) {
            throw new IllegalArgumentException("代理端口" + config.getProxyPort() + "已被使用");
        }
        
        // 设置默认值
        if (config.getTargetProtocol() == null) {
            config.setTargetProtocol("http");
        }
        
        if (config.getStatus() == null) {
            config.setStatus("ACTIVE");
        }
        
        if (config.getApiPathPatterns() == null) {
            config.setApiPathPatterns("/api/**,/rest/**");
        }
        
        if (config.getWatermarkOpacity() == null) {
            config.setWatermarkOpacity(0.1);
        }
        
        if (config.getWatermarkWidth() == null) {
            config.setWatermarkWidth(300);
        }
        
        if (config.getWatermarkHeight() == null) {
            config.setWatermarkHeight(150);
        }
        
        if (config.getWatermarkColor() == null) {
            config.setWatermarkColor("#666666");
        }
        
        if (config.getWatermarkAngle() == null) {
            config.setWatermarkAngle(-30.0);
        }
        
        if (config.getEnablePageWatermark() == null) {
            config.setEnablePageWatermark(true);
        }
        

        
        if (config.getEnableLinkRewrite() == null) {
            config.setEnableLinkRewrite(true);
        }
        
        if (config.getEnableApiIntercept() == null) {
            config.setEnableApiIntercept(true);
        }
    }
}
