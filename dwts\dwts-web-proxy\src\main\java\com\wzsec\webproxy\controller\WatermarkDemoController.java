package com.wzsec.webproxy.controller;

import com.wzsec.webproxy.domain.WebProxyConfig;
import com.wzsec.webproxy.watermark.impl.InvisibleWatermarkProcessor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;

/**
 * 水印演示控制器
 * 用于展示水印前后的文字对比
 *
 * <AUTHOR>
 * @date 2025/08/05
 */
@Slf4j
@RestController
@RequestMapping("/api/watermark/demo")
public class WatermarkDemoController {

    @Autowired
    private InvisibleWatermarkProcessor invisibleWatermarkProcessor;

    /**
     * 演示暗水印效果
     */
    @PostMapping("/invisible-demo")
    public ResponseEntity<Map<String, Object>> demonstrateInvisibleWatermark(
            @RequestBody DemoRequest request,
            HttpServletRequest httpRequest) {

        Map<String, Object> response = new HashMap<>();

        try {
            // 创建模拟配置
            WebProxyConfig config = createMockConfig();
            
            // 原始内容
            String originalContent = request.getContent();
            String contentType = request.getContentType() != null ? request.getContentType() : "application/json";

            log.info("演示暗水印 - 原始内容长度: {}, 类型: {}", originalContent.length(), contentType);

            // 处理水印
            byte[] watermarkedBytes = invisibleWatermarkProcessor.processWatermark(
                    originalContent.getBytes(StandardCharsets.UTF_8),
                    contentType,
                    httpRequest,
                    config
            );

            String watermarkedContent = new String(watermarkedBytes, StandardCharsets.UTF_8);

            // 分析差异
            Map<String, Object> analysis = analyzeWatermarkDifference(originalContent, watermarkedContent);

            // 构建响应
            Map<String, Object> result = new HashMap<>();
            result.put("original", originalContent);
            result.put("watermarked", watermarkedContent);
            result.put("originalLength", originalContent.length());
            result.put("watermarkedLength", watermarkedContent.length());
            result.put("lengthDifference", watermarkedContent.length() - originalContent.length());
            result.put("analysis", analysis);
            result.put("contentType", contentType);

            response.put("success", true);
            response.put("data", result);
            response.put("message", "暗水印演示完成");

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("暗水印演示失败", e);
            response.put("success", false);
            response.put("message", "演示失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    /**
     * 显示零宽字符详情
     */
    @PostMapping("/show-zero-width-chars")
    public ResponseEntity<Map<String, Object>> showZeroWidthCharacters(
            @RequestBody String content) {

        Map<String, Object> response = new HashMap<>();

        try {
            Map<String, Object> result = new HashMap<>();
            
            // 分析零宽字符
            Map<String, Integer> zeroWidthStats = new HashMap<>();
            StringBuilder visibleRepresentation = new StringBuilder();
            StringBuilder unicodeRepresentation = new StringBuilder();

            for (int i = 0; i < content.length(); i++) {
                char c = content.charAt(i);
                String charName = getZeroWidthCharName(c);
                
                if (charName != null) {
                    zeroWidthStats.merge(charName, 1, Integer::sum);
                    visibleRepresentation.append("[").append(charName).append("]");
                    unicodeRepresentation.append("\\u").append(String.format("%04X", (int) c));
                } else {
                    visibleRepresentation.append(c);
                    if (c > 127) {
                        unicodeRepresentation.append("\\u").append(String.format("%04X", (int) c));
                    } else {
                        unicodeRepresentation.append(c);
                    }
                }
            }

            result.put("originalContent", content);
            result.put("contentLength", content.length());
            result.put("visibleRepresentation", visibleRepresentation.toString());
            result.put("unicodeRepresentation", unicodeRepresentation.toString());
            result.put("zeroWidthCharStats", zeroWidthStats);
            result.put("hasZeroWidthChars", !zeroWidthStats.isEmpty());

            response.put("success", true);
            response.put("data", result);
            response.put("message", "零宽字符分析完成");

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("零宽字符分析失败", e);
            response.put("success", false);
            response.put("message", "分析失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    /**
     * 对比两个文本的差异
     */
    @PostMapping("/compare-texts")
    public ResponseEntity<Map<String, Object>> compareTexts(
            @RequestParam String originalText,
            @RequestParam String watermarkedText) {

        Map<String, Object> response = new HashMap<>();

        try {
            Map<String, Object> comparison = analyzeWatermarkDifference(originalText, watermarkedText);
            
            response.put("success", true);
            response.put("data", comparison);
            response.put("message", "文本对比完成");

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("文本对比失败", e);
            response.put("success", false);
            response.put("message", "对比失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    /**
     * 创建模拟配置
     */
    private WebProxyConfig createMockConfig() {
        WebProxyConfig config = new WebProxyConfig();
        config.setProxyName("demo-proxy");
        config.setEnableApiWatermark(true);
        config.setInvisibleEncodingStrength("medium");
        config.setInvisibleEmbedDensity(0.3);
        config.setWatermarkText("DWTS演示水印");
        return config;
    }

    /**
     * 分析水印差异
     */
    private Map<String, Object> analyzeWatermarkDifference(String original, String watermarked) {
        Map<String, Object> analysis = new HashMap<>();

        // 基本统计
        analysis.put("originalLength", original.length());
        analysis.put("watermarkedLength", watermarked.length());
        analysis.put("addedCharacters", watermarked.length() - original.length());

        // 查找插入的零宽字符位置
        Map<Integer, String> insertedChars = new HashMap<>();
        int originalIndex = 0;
        int watermarkedIndex = 0;

        while (originalIndex < original.length() && watermarkedIndex < watermarked.length()) {
            char originalChar = original.charAt(originalIndex);
            char watermarkedChar = watermarked.charAt(watermarkedIndex);

            if (originalChar == watermarkedChar) {
                originalIndex++;
                watermarkedIndex++;
            } else {
                // 发现插入的字符
                String charName = getZeroWidthCharName(watermarkedChar);
                if (charName != null) {
                    insertedChars.put(watermarkedIndex, charName);
                    watermarkedIndex++;
                } else {
                    // 字符不匹配且不是零宽字符，可能是替换
                    originalIndex++;
                    watermarkedIndex++;
                }
            }
        }

        // 处理末尾的插入字符
        while (watermarkedIndex < watermarked.length()) {
            char watermarkedChar = watermarked.charAt(watermarkedIndex);
            String charName = getZeroWidthCharName(watermarkedChar);
            if (charName != null) {
                insertedChars.put(watermarkedIndex, charName);
            }
            watermarkedIndex++;
        }

        analysis.put("insertedPositions", insertedChars);
        analysis.put("totalInsertedChars", insertedChars.size());

        // 统计零宽字符类型
        Map<String, Integer> charTypeStats = new HashMap<>();
        for (String charName : insertedChars.values()) {
            charTypeStats.merge(charName, 1, Integer::sum);
        }
        analysis.put("zeroWidthCharTypes", charTypeStats);

        return analysis;
    }

    /**
     * 获取零宽字符名称
     */
    private String getZeroWidthCharName(char c) {
        switch (c) {
            case '\u200B': return "ZERO_WIDTH_SPACE";
            case '\u200C': return "ZERO_WIDTH_NON_JOINER";
            case '\u200D': return "ZERO_WIDTH_JOINER";
            case '\u2060': return "WORD_JOINER";
            case '\u2062': return "INVISIBLE_SEPARATOR";
            default: return null;
        }
    }

    /**
     * 演示请求
     */
    public static class DemoRequest {
        private String content;
        private String contentType;

        // Getters and Setters
        public String getContent() { return content; }
        public void setContent(String content) { this.content = content; }

        public String getContentType() { return contentType; }
        public void setContentType(String contentType) { this.contentType = contentType; }
    }
}
