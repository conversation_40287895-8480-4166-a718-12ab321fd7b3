package com.wzsec.webproxy.watermark;

import com.wzsec.webproxy.domain.WebProxyConfig;
import com.wzsec.webproxy.watermark.impl.InvisibleWatermarkProcessor;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.nio.charset.StandardCharsets;

import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

/**
 * API水印（暗水印）打印功能测试
 *
 * <AUTHOR>
 * @date 2025/08/06
 */
@Slf4j
@SpringBootTest
@ActiveProfiles("test")
public class InvisibleWatermarkPrintTest {

    @Test
    public void testJsonWatermarkPrint() {
        // 创建处理器
        InvisibleWatermarkProcessor processor = new InvisibleWatermarkProcessor();
        
        // 创建模拟配置
        WebProxyConfig config = createMockConfig();
        
        // 创建模拟请求
        HttpServletRequest request = createMockRequest();
        
        // 测试JSON内容
        String jsonContent = """
            {
                "code": 200,
                "message": "success",
                "data": [
                    {"id": 1, "name": "张三", "email": "<EMAIL>"},
                    {"id": 2, "name": "李四", "email": "<EMAIL>"}
                ],
                "total": 2
            }
            """;
        
        log.info("开始测试JSON水印打印功能");
        
        // 处理水印
        byte[] result = processor.processWatermark(
                jsonContent.getBytes(StandardCharsets.UTF_8),
                "application/json",
                request,
                config
        );
        
        log.info("JSON水印测试完成，结果长度: {} bytes", result.length);
    }

    @Test
    public void testXmlWatermarkPrint() {
        // 创建处理器
        InvisibleWatermarkProcessor processor = new InvisibleWatermarkProcessor();
        
        // 创建模拟配置
        WebProxyConfig config = createMockConfig();
        
        // 创建模拟请求
        HttpServletRequest request = createMockRequest();
        
        // 测试XML内容
        String xmlContent = """
            <?xml version="1.0" encoding="UTF-8"?>
            <response>
                <code>200</code>
                <message>success</message>
                <data>
                    <item>
                        <id>1</id>
                        <name>张三</name>
                        <email><EMAIL></email>
                    </item>
                    <item>
                        <id>2</id>
                        <name>李四</name>
                        <email><EMAIL></email>
                    </item>
                </data>
                <total>2</total>
            </response>
            """;
        
        log.info("开始测试XML水印打印功能");
        
        // 处理水印
        byte[] result = processor.processWatermark(
                xmlContent.getBytes(StandardCharsets.UTF_8),
                "application/xml",
                request,
                config
        );
        
        log.info("XML水印测试完成，结果长度: {} bytes", result.length);
    }

    private WebProxyConfig createMockConfig() {
        WebProxyConfig config = new WebProxyConfig();
        config.setProxyName("test-proxy");
        config.setEnableApiWatermark(true);
        config.setInvisibleEncodingStrength("medium");
        config.setInvisibleEmbedDensity(0.3);
        config.setWatermarkText("DWTS测试水印");
        return config;
    }

    private HttpServletRequest createMockRequest() {
        HttpServletRequest request = mock(HttpServletRequest.class);
        HttpSession session = mock(HttpSession.class);
        
        when(request.getRemoteAddr()).thenReturn("*************");
        when(request.getHeader("X-Forwarded-For")).thenReturn(null);
        when(request.getHeader("X-Real-IP")).thenReturn(null);
        when(request.getRequestURI()).thenReturn("/api/test");
        when(request.getSession()).thenReturn(session);
        when(session.getId()).thenReturn("test-session-123");
        when(request.getHeader("Authorization")).thenReturn("Bearer test-token");
        
        return request;
    }
}
