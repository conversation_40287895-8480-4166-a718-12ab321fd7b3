package com.wzsec.webproxy.watermark;

import com.wzsec.webproxy.watermark.impl.HtmlWatermarkProcessor;
import com.wzsec.webproxy.watermark.impl.InvisibleWatermarkProcessor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.List;

/**
 * 水印处理器工厂
 * 只保留页面水印和不可见字符水印处理器
 *
 * <AUTHOR>
 * @date 2025/08/05
 */
@Slf4j
@Component
public class WatermarkProcessorFactory {

    @Autowired
    private HtmlWatermarkProcessor htmlWatermarkProcessor;

    @Autowired
    private InvisibleWatermarkProcessor invisibleWatermarkProcessor;

    private List<WatermarkProcessor> processors;

    @PostConstruct
    public void initProcessors() {
        processors = new ArrayList<>();
        processors.add(htmlWatermarkProcessor);
        processors.add(invisibleWatermarkProcessor);

        log.info("初始化水印处理器完成，共{}个处理器", processors.size());
        for (WatermarkProcessor processor : processors) {
            log.info("- {}: {}", processor.getProcessorName(), processor.getWatermarkType());
        }
    }

    /**
     * 根据内容类型获取合适的水印处理器
     *
     * @param contentType 内容类型
     * @return 水印处理器
     */
    public WatermarkProcessor getProcessor(String contentType) {
        if (contentType == null) {
            log.debug("内容类型为空，使用默认处理器");
            return headerWatermarkProcessor;
        }

        // 遍历所有处理器，找到第一个能处理该内容类型的处理器
        for (WatermarkProcessor processor : processors) {
            if (processor.canHandle(contentType)) {
                log.debug("内容类型[{}]使用处理器[{}]", contentType, processor.getProcessorName());
                return processor;
            }
        }

        // 如果没有找到合适的处理器，使用默认的Header处理器
        log.debug("未找到适合内容类型[{}]的处理器，使用默认处理器", contentType);
        return headerWatermarkProcessor;
    }

    /**
     * 根据内容类型和配置获取合适的水印处理器
     *
     * @param contentType 内容类型
     * @param config 代理配置
     * @return 水印处理器
     */
    public WatermarkProcessor getProcessor(String contentType, com.wzsec.webproxy.domain.WebProxyConfig config) {
        if (contentType == null) {
            log.debug("内容类型为空，使用默认处理器");
            return headerWatermarkProcessor;
        }

        // 优先选择暗水印处理器（如果启用）
        if (config.getEnableInvisibleWatermark() && invisibleWatermarkProcessor.canHandle(contentType)) {
            log.debug("内容类型[{}]使用暗水印处理器", contentType);
            return invisibleWatermarkProcessor;
        }

        // 其次选择常规水印处理器（如果启用API水印）
        if (config.getEnableApiWatermark()) {
            for (WatermarkProcessor processor : processors) {
                // 跳过暗水印处理器和Header处理器
                if (processor != invisibleWatermarkProcessor &&
                    processor != headerWatermarkProcessor &&
                    processor.canHandle(contentType)) {
                    log.debug("内容类型[{}]使用常规水印处理器[{}]", contentType, processor.getProcessorName());
                    return processor;
                }
            }
        }

        // 最后使用Header处理器作为默认
        log.debug("内容类型[{}]使用默认Header处理器", contentType);
        return headerWatermarkProcessor;
    }

    /**
     * 获取HTML处理器
     */
    public HtmlWatermarkProcessor getHtmlProcessor() {
        return htmlWatermarkProcessor;
    }

    /**
     * 获取JSON处理器
     */
    public JsonWatermarkProcessor getJsonProcessor() {
        return jsonWatermarkProcessor;
    }

    /**
     * 获取暗水印处理器
     */
    public InvisibleWatermarkProcessor getInvisibleProcessor() {
        return invisibleWatermarkProcessor;
    }

    /**
     * 获取XML处理器
     */
    public XmlWatermarkProcessor getXmlProcessor() {
        return xmlWatermarkProcessor;
    }

    /**
     * 获取Header处理器
     */
    public HeaderWatermarkProcessor getHeaderProcessor() {
        return headerWatermarkProcessor;
    }

    /**
     * 获取所有处理器
     */
    public List<WatermarkProcessor> getAllProcessors() {
        return new ArrayList<>(processors);
    }

    /**
     * 根据处理器名称获取处理器
     *
     * @param processorName 处理器名称
     * @return 水印处理器，如果未找到返回null
     */
    public WatermarkProcessor getProcessorByName(String processorName) {
        if (processorName == null) {
            return null;
        }

        for (WatermarkProcessor processor : processors) {
            if (processorName.equals(processor.getProcessorName())) {
                return processor;
            }
        }

        return null;
    }

    /**
     * 根据水印类型获取处理器
     *
     * @param watermarkType 水印类型
     * @return 水印处理器，如果未找到返回null
     */
    public WatermarkProcessor getProcessorByType(String watermarkType) {
        if (watermarkType == null) {
            return null;
        }

        for (WatermarkProcessor processor : processors) {
            if (watermarkType.equals(processor.getWatermarkType())) {
                return processor;
            }
        }

        return null;
    }
}
