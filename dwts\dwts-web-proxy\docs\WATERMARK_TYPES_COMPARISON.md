# 水印类型说明

## 📋 概述

DWTS Web代理系统支持两种水印技术：**页面水印**和**不可见字符水印**。

## 🔍 详细说明

### 1. 页面水印 (enablePageWatermark)

#### 特点
- **可见性**：在HTML页面上显示可见的水印
- **实现方式**：通过CSS和JavaScript在页面上叠加水印层
- **用户感知**：用户可以看到水印，但不影响页面功能

#### 配置
```yaml
# 数据库配置
enable_page_watermark: true

# 应用配置
web-proxy:
  watermark:
    default-text: "DWTS水印"
    default-opacity: 0.1
    default-width: 300
    default-height: 150
    default-color: "#666666"
    default-angle: -30
```

### 2. API水印/暗水印 (enableApiWatermark)

#### 特点
- **完全不可见**：使用零宽字符技术，用户完全感知不到
- **高级溯源**：支持复制粘贴追踪，即使内容被转发也能溯源
- **防篡改**：包含校验和，确保水印完整性

#### 实现原理
```java
// 零宽字符定义
private static final char ZERO_WIDTH_SPACE = '\u200B';           // 零宽空格
private static final char ZERO_WIDTH_NON_JOINER = '\u200C';      // 零宽非连接符
private static final char ZERO_WIDTH_JOINER = '\u200D';          // 零宽连接符
private static final char WORD_JOINER = '\u2060';                // 词连接符
```

#### 示例
```json
// 用户看到的响应（完全正常）
{
  "data": [
    {"id": 1, "name": "张三"},
    {"id": 2, "name": "李四"}
  ]
}

// 实际响应（包含零宽字符，但不可见）
{
  "data": [
    {"id": 1, "name": "张​‌‍⁠三"},  // 包含零宽字符编码的用户信息
    {"id": 2, "name": "李​‌‍⁠四"}   // 包含零宽字符编码的访问信息
  ]
}
```

#### 配置
```yaml
# 数据库配置
enable_api_watermark: true
invisible_encoding_strength: "medium"
invisible_embed_density: 0.3

# 应用配置
web-proxy:
  watermark:
    invisible:
      enabled: true
      encoding-strength: medium  # low, medium, high
      embed-density: 0.3
      max-embed-length: 1000
```

## 🚀 处理器优先级

系统会根据配置自动选择合适的处理器：

```java
// 处理器选择逻辑
public WatermarkProcessor getProcessor(String contentType, WebProxyConfig config) {
    // 1. 优先选择API水印（暗水印）处理器（如果启用）
    if (config.getEnableApiWatermark() && invisibleWatermarkProcessor.canHandle(contentType)) {
        return invisibleWatermarkProcessor;
    }

    // 2. 对于HTML内容，使用页面水印处理器
    if (isHtmlContent(contentType) && config.getEnablePageWatermark()) {
        return htmlWatermarkProcessor;
    }

    // 3. 默认使用API水印处理器
    return invisibleWatermarkProcessor;
}
```

## 📊 配置组合建议

### 高安全级别
```sql
UPDATE dwts_web_proxy_config SET 
  enable_api_watermark = false,           -- 关闭可见水印
  enable_invisible_watermark = true,      -- 启用暗水印
  invisible_encoding_strength = 'high',   -- 高强度编码
  invisible_embed_density = 0.8;          -- 高密度嵌入
```

### 平衡级别
```sql
UPDATE dwts_web_proxy_config SET 
  enable_api_watermark = true,            -- 启用可见水印
  enable_invisible_watermark = true,      -- 同时启用暗水印
  invisible_encoding_strength = 'medium', -- 中等强度
  invisible_embed_density = 0.3;          -- 中等密度
```

### 基础级别
```sql
UPDATE dwts_web_proxy_config SET 
  enable_api_watermark = true,            -- 只启用可见水印
  enable_invisible_watermark = false,     -- 关闭暗水印
  invisible_encoding_strength = 'low',    -- 低强度
  invisible_embed_density = 0.1;          -- 低密度
```

## 🔧 溯源能力对比

| 特性 | API水印 | 暗水印 |
|------|---------|--------|
| **复制粘贴追踪** | ❌ 容易被删除 | ✅ 完全保留 |
| **跨平台传播** | ❌ 格式转换时丢失 | ✅ 任何文本传播都保留 |
| **用户感知** | ⚠️ 可见，可能被删除 | ✅ 完全不可见 |
| **技术门槛** | 🟢 低 | 🔴 高 |
| **溯源精度** | 🟡 中等 | 🟢 高 |
| **防篡改能力** | 🟡 中等 | 🟢 强 |

## 💡 最佳实践

1. **生产环境推荐**：启用暗水印，关闭API水印
2. **测试环境**：可以同时启用两种水印进行对比测试
3. **高敏感数据**：必须启用暗水印，设置高强度编码
4. **性能敏感场景**：可以只启用API水印或Header水印

## 🛠️ 故障排查

### 问题：API水印启用但暗水印不工作
```bash
# 检查配置
SELECT enable_api_watermark, enable_invisible_watermark 
FROM dwts_web_proxy_config WHERE proxy_name = 'your-proxy';

# 应该看到：
# enable_api_watermark: true/false
# enable_invisible_watermark: true  <- 这个必须是true
```

### 问题：日志显示"API水印启用: false"
这是正常的！这个日志只显示 `enableApiWatermark` 的状态，不影响暗水印的工作。

### 验证暗水印是否工作
```bash
# 调用溯源API检测
curl -X POST "http://localhost:9090/api/watermark/trace/quick-detect" \
  -H "Content-Type: application/json" \
  -d "复制的API响应内容"
```
